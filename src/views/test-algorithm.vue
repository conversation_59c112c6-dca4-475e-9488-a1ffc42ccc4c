<template>
  <div class="test-algorithm-page">
    <h1>算法模型管理测试页面</h1>

    <!-- 测试表单配置 -->
    <div class="test-section">
      <h2>表单字段配置测试</h2>
      <el-form ref="testForm" :model="testForm" :rules="testRules" label-width="120px">
        <el-form-item label="算法编码" prop="code">
          <el-input v-model="testForm.code" placeholder="请输入算法编码" />
        </el-form-item>
        <el-form-item label="算法名称" prop="name">
          <el-input v-model="testForm.name" placeholder="请输入算法名称" />
        </el-form-item>
        <el-form-item label="算法类型" prop="type">
          <el-select v-model="testForm.type" placeholder="请选择算法类型">
            <el-option
              v-for="item in algorithmTypeDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="testForm.status" placeholder="请选择状态">
            <el-option
              v-for="item in algorithmStatusDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交测试</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 测试卡片展示 -->
    <div class="test-section">
      <h2>卡片展示测试</h2>
      <div class="card-grid">
        <div
          v-for="(item, index) in mockData"
          :key="item.id || index"
          class="algorithm-card"
        >
          <el-card shadow="hover" class="card-item">
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="algorithm-info">
                <div class="algorithm-icon">
                  <i :class="getAlgorithmIcon(item.type)" />
                </div>
                <div class="algorithm-details">
                  <h3 class="algorithm-name">{{ item.name }}</h3>
                  <p class="algorithm-code">{{ item.code }}</p>
                </div>
              </div>
              <div class="algorithm-status">
                <el-tag
                  :type="getStatusType(item.status)"
                  size="small"
                >
                  {{ getStatusLabel(item.status) }}
                </el-tag>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">使用次数</span>
                  <span class="info-value">{{ item.usageCount || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">参数量</span>
                  <span class="info-value">{{ item.paramCount || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">类型</span>
                  <span class="info-value">{{ getTypeLabel(item.type) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">创建人</span>
                  <span class="info-value">{{ item.creator || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 卡片操作 -->
            <div class="card-actions">
              <el-button type="text" size="small" icon="el-icon-setting">
                参数
              </el-button>
              <el-button type="text" size="small" icon="el-icon-edit">
                编辑
              </el-button>
              <el-button type="text" size="small" :icon="item.status === 'enabled' ? 'el-icon-video-pause' : 'el-icon-video-play'">
                {{ item.status === 'enabled' ? '停用' : '启用' }}
              </el-button>
              <el-button type="text" size="small" icon="el-icon-delete" class="danger-btn">
                删除
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { algorithmType, algorithmStatus } from '@/dicts/video/index.js'

export default {
  name: 'TestAlgorithmPage',
  data() {
    return {
      // 字典数据
      algorithmTypeDict: algorithmType,
      algorithmStatusDict: algorithmStatus,

      // 测试表单
      testForm: {
        code: '',
        name: '',
        type: '',
        status: ''
      },

      // 表单验证规则
      testRules: {
        code: [
          { required: true, message: '算法编码不能为空', trigger: 'blur' },
          { min: 3, max: 20, message: '算法编码长度必须介于 3 和 20 之间', trigger: 'blur' },
          { pattern: /^[A-Z][A-Z0-9_]*$/, message: '算法编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '算法名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '算法名称长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择算法类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },

      // 模拟数据
      mockData: [
        {
          id: 1,
          code: 'ALG001',
          name: 'H264编码转换算法',
          usageCount: 156,
          paramCount: 8,
          type: 'encode_convert',
          status: 'enabled',
          creator: '张三'
        },
        {
          id: 2,
          code: 'ALG002',
          name: '视频降噪预处理',
          usageCount: 89,
          paramCount: 12,
          type: 'data_preprocess',
          status: 'enabled',
          creator: '李四'
        },
        {
          id: 3,
          code: 'ALG003',
          name: '智能场景识别',
          usageCount: 234,
          paramCount: 15,
          type: 'deep_process',
          status: 'disabled',
          creator: '王五'
        }
      ]
    }
  },
  methods: {
    // 获取算法图标
    getAlgorithmIcon(type) {
      const iconMap = {
        'encode_convert': 'el-icon-refresh',
        'data_preprocess': 'el-icon-magic-stick',
        'deep_process': 'el-icon-cpu'
      }
      return iconMap[type] || 'el-icon-document'
    },
    // 获取状态类型
    getStatusType(status) {
      return status === 'enabled' ? 'success' : 'info'
    },
    // 获取状态标签
    getStatusLabel(status) {
      const statusItem = this.algorithmStatusDict.find(item => item.value === status)
      return statusItem ? statusItem.label : status
    },
    // 获取类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    // 提交测试
    handleSubmit() {
      this.$refs.testForm?.validate((valid) => {
        if (valid) {
          this.$message.success('表单验证通过！')
          console.log('表单数据:', this.testForm)
        } else {
          this.$message.error('表单验证失败！')
        }
      })
    },
    // 重置表单
    handleReset() {
      this.testForm = {
        code: '',
        name: '',
        type: '',
        status: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-algorithm-page {
  padding: 20px;

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    h2 {
      margin-bottom: 20px;
      color: #303133;
    }
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
  }

  .algorithm-card {
    .card-item {
      height: 100%;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .algorithm-info {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .algorithm-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .algorithm-details {
        flex: 1;
        min-width: 0;

        .algorithm-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 4px 0;
          line-height: 1.4;
          word-break: break-word;
        }

        .algorithm-code {
          font-size: 13px;
          color: #909399;
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
    }

    .algorithm-status {
      flex-shrink: 0;
      margin-left: 12px;
    }
  }

  .card-content {
    margin-bottom: 16px;

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px 16px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f5f7fa;

        .info-label {
          font-size: 13px;
          color: #909399;
          font-weight: 500;
        }

        .info-value {
          font-size: 13px;
          color: #606266;
          font-weight: 600;
          text-align: right;
          word-break: break-word;
        }
      }
    }
  }

  .card-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #f5f7fa;

    .el-button {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 4px;

      &.danger-btn {
        color: #f56c6c;

        &:hover {
          color: #f56c6c;
          background-color: #fef0f0;
          border-color: #fbc4c4;
        }
      }
    }
  }
}
</style>
