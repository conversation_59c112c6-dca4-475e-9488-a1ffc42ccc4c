<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after="{ selected }">
      <el-button
        type="primary"
        size="small"
        :disabled="!selected || selected.length === 0"
        @click="handleReExtraction"
      >
        重新萃取
      </el-button>
    </template>

    <!-- 萃取状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getExtractionStatusTagType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value11:simple="{ row }">
      <el-tag type="info">
        {{ row.value11 }}
      </el-tag>
    </template>

    <!-- 剔除黑屏列自定义渲染 -->
    <template #table:value5:simple="{ row }">
      <el-tag :type="row.value5 === '是' ? 'success' : 'info'">
        {{ row.value5 }}
      </el-tag>
    </template>

    <!-- 剔除花屏列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag :type="row.value6 === '是' ? 'success' : 'info'">
        {{ row.value6 }}
      </el-tag>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus, extractionStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoExtractionManagement',
  data() {
    return {
      tableType: 'scheduled_task_queue'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频萃取管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 视频文件 ID
          value1: {
            type: 'text',
            label: '视频文件ID',
            align: 'left',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 任务编号
          value2: {
            type: 'text',
            label: '任务编号',
            align: 'left',
            width: 150,
            search: {
              hidden: true
            }
          },
          // 视频文件名
          value3: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            search: {
              placeholder: '请输入文件名'
            }
          },
          // 文件大小
          value4: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 剔除黑屏
          value5: {
            type: 'text',
            label: '剔除黑屏',
            width: 100,
            search: {
              hidden: true
            }
          },
          // 剔除花屏
          value6: {
            type: 'text',
            label: '剔除花屏',
            width: 100,
            search: {
              hidden: true
            }
          },
          // 剔除后视频文件名
          value7: {
            type: 'text',
            label: '剔除后视频文件名',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 萃取时间
          value8: {
            type: 'datetime',
            label: '萃取时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          // 萃取状态
          value9: {
            type: 'select',
            label: '萃取状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...extractionStatus
              ]
            },
            options: extractionStatus
          },
          // 失败原因
          value10: {
            type: 'text',
            label: '失败原因',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 文件格式（搜索用）
          value11: {
            type: 'select',
            label: '文件格式',
            width: 100,
            hidden: true,
            search: {
              hidden: false,
              type: 'select',
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            options: fileFormat
          },
          // 文件状态（搜索用）
          value12: {
            type: 'select',
            label: '文件状态',
            width: 100,
            hidden: true,
            search: {
              hidden: false,
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            options: fileStatus
          }
        }
      }
    }
  },
  methods: {
    // 获取萃取状态标签类型
    getExtractionStatusTagType(status) {
      const statusMap = {
        '进行中': 'warning',
        '成功': 'success',
        '失败': 'danger',
        '待处理': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 重新萃取
    handleReExtraction() {
      const selected = this.$refs.sheetRef.tableMixin.selected
      if (!selected || selected.length === 0) {
        this.$modal.msgWarning('请选择要重新萃取的视频文件')
        return
      }

      const fileNames = selected.map(item => item.value3).join('、')
      this.$modal.confirm(`确认要重新萃取选中的 ${selected.length} 个视频文件吗？\n文件：${fileNames}`).then(() => {
        // 调用重新萃取接口
        const ids = selected.map(item => item.value1)
        this.reExtractionVideos(ids)
      }).catch(() => {})
    },

    // 调用重新萃取接口
    async reExtractionVideos(ids) {
      try {
        await request({
          url: '/system/AutoOsmotic/reExtraction',
          method: 'post',
          data: {
            ids: ids,
            type: this.tableType
          }
        })
        this.$modal.msgSuccess('重新萃取任务已提交')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        this.$modal.msgError('重新萃取失败：' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
