import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import { setToken, setHarborToken } from '@/utils/auth'
import request from '@/utils/request'

function saveModuleId(moduleId) {
  request({
    url: '/system/user/saveModuleId',
    method: 'get',
    params: {
      moduleId
    },
    loading: true
  }).then((response) => { })
}

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register', '/test-algorithm']

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      // 可能需要更换token
      if (
        to.query.Authorization &&
        to.query.HarborToken &&
        to.query.statusCode == 200
      ) {
        setToken(to.query.Authorization)
        setHarborToken(to.query.HarborToken)
      }
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表

            if (to.meta.menuId) {
              saveModuleId(to.meta.menuId)
            }

            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        if (to.meta.menuId) {
          saveModuleId(to.meta.menuId)
        }

        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      if (
        to.query.Authorization &&
        to.query.HarborToken &&
        to.query.statusCode == 200
      ) {
        // 如果登录带有token
        setToken(to.query.Authorization)
        setHarborToken(to.query.HarborToken)

        next({ path: '/' })
        NProgress.done()
      } else {
        if (to.meta.menuId) {
          saveModuleId(to.meta.menuId)
        }

        next()
      }
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
